const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const cacheService = require('../../../../services/cache');

module.exports = async (req, res) => {
  try {
    const { id } = req.params;
    const { destinationFolderId } = req.body;

    if (!id || !destinationFolderId) {
      return res.status(400).json({
        code: CONSTANTS.CODE.INVALID_PARAMS,
        message: 'Item ID and destinationFolderId are required'
      });
    }

    const FileModel = require('../../../../models/file');
    const FolderModel = require('../../../../models/folder');

    // Try to find as file first
    let item = await FileModel.findOne({
      _id: id,
      isDeleted: false
    });

    let itemType = 'file';
    let updateResult;
    let oldParentId = null;

    if (item) {
      oldParentId = item.parentId;
      // It's a file - update parentId
      updateResult = await FileModel.updateOne(
        { _id: id, isDeleted: false },
        { parentId: destinationFolderId }
      );
    } else {
      // Try to find as folder
      item = await FolderModel.findOne({
        _id: id,
        isDeleted: false
      });

      if (!item) {
        return res.status(404).json({
          code: CONSTANTS.CODE.NOT_FOUND,
          message: 'Item not found'
        });
      }
      itemType = 'folder';
      oldParentId = item.parentId;

      // Không cho phép di chuyển folder vào chính nó hoặc folder con của nó
      if (id === destinationFolderId) {
        return res.status(400).json({
          code: CONSTANTS.CODE.INVALID_PARAMS,
          message: 'Cannot move a folder into itself'
        });
      }
      // Kiểm tra folder đích có phải là con của folder hiện tại không
      let current = await FolderModel.findOne({ _id: destinationFolderId, isDeleted: false });
      while (current) {
        if (current.parentId && current.parentId.toString() === id) {
          return res.status(400).json({
            code: CONSTANTS.CODE.INVALID_PARAMS,
            message: 'Cannot move a folder into its subfolder'
          });
        }
        if (!current.parentId) break;
        current = await FolderModel.findOne({ _id: current.parentId, isDeleted: false });
      }
      // Update parentId
      updateResult = await FolderModel.updateOne(
        { _id: id, isDeleted: false },
        { parentId: destinationFolderId }
      );
    }

    if (updateResult.matchedCount === 0) {
      return res.status(404).json({
        code: CONSTANTS.CODE.NOT_FOUND,
        message: 'Item not found'
      });
    }

    // Clear cache for old and new parent folder
    try {
      await cacheService.clearFolderCache(oldParentId);
      await cacheService.clearFolderCache(destinationFolderId);
      await cacheService.clearSearchCache();
    } catch (cacheError) {
      console.warn('Cache clear failed:', cacheError.message);
    }

    global.logger.logInfo(`${itemType} moved successfully: ID ${id} to folder ${destinationFolderId}`);

    return res.json({
      code: CONSTANTS.CODE.SUCCESS,
      data: {
        id: id,
        type: itemType,
        oldParentId,
        newParentId: destinationFolderId
      },
      message: `${itemType === 'file' ? 'File' : 'Folder'} moved successfully`
    });
  } catch (error) {
    global.logger.logInfo(['items/move error', error.message], __dirname);
    console.error('Move error:', error);
    return res.status(500).json({
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });
  }
};