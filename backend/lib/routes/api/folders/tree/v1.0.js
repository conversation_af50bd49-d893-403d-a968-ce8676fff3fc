const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');

module.exports = async (req, res) => {
  try {
    const userId = req.user ? req.user.id : null;
    const { excludeId } = req.query;

    if (!userId) {
      return res.status(401).json({
        code: CONSTANTS.CODE.TOKEN_EXPIRE,
        message: 'User authentication required'
      });
    }

    const FolderModel = require('../../../../models/folder');

    // Get all folders for the user
    const folders = await FolderModel.find({
      ownerId: userId,
      isDeleted: false
    }).select('_id folderName parentId createdAt').lean();

    // Function to get all descendant folder IDs
    const getDescendantIds = (folderId) => {
      const descendants = new Set();
      const findDescendants = (parentId) => {
        folders.forEach(folder => {
          if (folder.parentId && folder.parentId.toString() === parentId) {
            const childId = folder._id.toString();
            descendants.add(childId);
            findDescendants(childId);
          }
        });
      };
      findDescendants(folderId);
      return descendants;
    };

    // Get excluded folder IDs (excludeId and all its descendants)
    const excludedIds = new Set();
    if (excludeId) {
      excludedIds.add(excludeId);
      const descendants = getDescendantIds(excludeId);
      descendants.forEach(id => excludedIds.add(id));
    }

    // Filter out excluded folders
    const filteredFolders = folders.filter(folder =>
      !excludedIds.has(folder._id.toString())
    );

    // Build tree structure
    const buildTree = (parentId = null) => {
      return filteredFolders
        .filter(folder => {
          if (parentId === null) {
            return folder.parentId === null || folder.parentId === undefined;
          }
          return folder.parentId && folder.parentId.toString() === parentId;
        })
        .map(folder => ({
          id: folder._id.toString(),
          name: folder.folderName,
          parentId: folder.parentId ? folder.parentId.toString() : null,
          children: buildTree(folder._id.toString()),
          hasChildren: filteredFolders.some(f => f.parentId && f.parentId.toString() === folder._id.toString()),
          createdAt: folder.createdAt
        }))
        .sort((a, b) => a.name.localeCompare(b.name));
    };

    const tree = buildTree();

    // Add root folder as virtual node
    const rootNode = {
      id: null,
      name: 'Root Folder',
      parentId: null,
      children: tree,
      hasChildren: tree.length > 0,
      isRoot: true
    };

    global.logger.logInfo(`Folder tree retrieved for user ${userId}: ${filteredFolders.length}/${folders.length} folders (excluded: ${excludedIds.size})`);

    return res.json({
      code: CONSTANTS.CODE.SUCCESS,
      data: {
        tree: rootNode,
        totalFolders: filteredFolders.length,
        excludedCount: excludedIds.size
      },
      message: 'Folder tree retrieved successfully'
    });

  } catch (error) {
    global.logger.logInfo(['folders/tree error', error.message], __dirname);
    console.error('Folder tree error:', error);

    return res.status(500).json({
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });
  }
};
