import React, { useState } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  List,
  ListItem,
  ListItemText,
  Typography,
  ListItemButton,
  ListItemIcon,
  Divider,
  Box,
  Chip
} from '@mui/material';
import {
  Folder as FolderIcon,
  Home as HomeIcon,
  FolderOpen as FolderOpenIcon
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { FolderItem } from '../types';

interface MoveDialogProps {
  open: boolean;
  onClose: () => void;
  onMove: (destinationFolderId: string | null) => void;
  folders: FolderItem[];
  excludeId?: string; // Để loại trừ folder hiện tại và con của nó
  currentFolderId?: string | null; // Folder hiện tại để highlight
}

const MoveDialog: React.FC<MoveDialogProps> = ({
  open,
  onClose,
  onMove,
  folders,
  excludeId,
  currentFolderId
}) => {
  const { t } = useTranslation();
  const [selectedFolderId, setSelectedFolderId] = useState<string | null>(null);

  // Loại trừ folder hiện tại và con của nó nếu cần
  const filteredFolders = excludeId
    ? folders.filter(f => f.id !== excludeId && f.parentId !== excludeId)
    : folders;

  // Reset selection when dialog opens
  React.useEffect(() => {
    if (open) {
      setSelectedFolderId(null);
    }
  }, [open]);

  const handleMoveToRoot = () => {
    setSelectedFolderId('ROOT');
  };

  const handleMoveToFolder = (folderId: string) => {
    setSelectedFolderId(folderId);
  };

  const handleConfirmMove = () => {
    if (selectedFolderId === 'ROOT') {
      onMove(null); // Move to root
    } else if (selectedFolderId) {
      onMove(selectedFolderId);
    }
  };

  const isRootSelected = selectedFolderId === 'ROOT';
  const isCurrentLocation = (folderId: string | null) => {
    if (folderId === null && currentFolderId === null) return true;
    return folderId === currentFolderId;
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <FolderOpenIcon color="primary" />
          {t('moveDialog.title', 'Move to Folder')}
        </Box>
      </DialogTitle>
      <DialogContent>
        <Typography variant="body2" sx={{ mb: 2, color: 'text.secondary' }}>
          {t('moveDialog.selectDestination', 'Select a destination folder:')}
        </Typography>

        <List sx={{ maxHeight: 300, overflow: 'auto' }}>
          {/* Root folder option */}
          <ListItemButton
            selected={isRootSelected}
            onClick={handleMoveToRoot}
            disabled={isCurrentLocation(null)}
            sx={{
              borderRadius: 1,
              mb: 1,
              '&.Mui-selected': {
                backgroundColor: 'primary.light',
                '&:hover': {
                  backgroundColor: 'primary.main',
                },
              },
            }}
          >
            <ListItemIcon>
              <HomeIcon color={isRootSelected ? 'primary' : 'inherit'} />
            </ListItemIcon>
            <ListItemText
              primary={t('moveDialog.rootFolder', 'Root Folder')}
              secondary={t('moveDialog.rootDescription', 'Move to the main directory')}
            />
            {isCurrentLocation(null) && (
              <Chip
                label={t('moveDialog.current', 'Current')}
                size="small"
                color="default"
                sx={{ ml: 1 }}
              />
            )}
          </ListItemButton>

          {filteredFolders.length > 0 && (
            <>
              <Divider sx={{ my: 1 }} />
              {filteredFolders.map(folder => (
                <ListItemButton
                  key={folder.id}
                  selected={selectedFolderId === folder.id}
                  onClick={() => handleMoveToFolder(folder.id)}
                  disabled={isCurrentLocation(folder.id)}
                  sx={{
                    borderRadius: 1,
                    mb: 0.5,
                    '&.Mui-selected': {
                      backgroundColor: 'primary.light',
                      '&:hover': {
                        backgroundColor: 'primary.main',
                      },
                    },
                  }}
                >
                  <ListItemIcon>
                    <FolderIcon color={selectedFolderId === folder.id ? 'primary' : 'inherit'} />
                  </ListItemIcon>
                  <ListItemText
                    primary={folder.name}
                    secondary={`${t('moveDialog.folder', 'Folder')} • ${folder.name}`}
                  />
                  {isCurrentLocation(folder.id) && (
                    <Chip
                      label={t('moveDialog.current', 'Current')}
                      size="small"
                      color="default"
                      sx={{ ml: 1 }}
                    />
                  )}
                </ListItemButton>
              ))}
            </>
          )}
        </List>

        {filteredFolders.length === 0 && (
          <Box sx={{ textAlign: 'center', py: 3, color: 'text.secondary' }}>
            <FolderIcon sx={{ fontSize: 48, mb: 1, opacity: 0.5 }} />
            <Typography variant="body2">
              {t('moveDialog.noFolders', 'No folders available')}
            </Typography>
          </Box>
        )}
      </DialogContent>
      <DialogActions sx={{ px: 3, pb: 2 }}>
        <Button onClick={onClose} color="inherit">
          {t('common.cancel')}
        </Button>
        <Button
          onClick={handleConfirmMove}
          disabled={!selectedFolderId}
          variant="contained"
          startIcon={selectedFolderId === 'ROOT' ? <HomeIcon /> : <FolderIcon />}
        >
          {t('moveDialog.move', 'Move')}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default MoveDialog;