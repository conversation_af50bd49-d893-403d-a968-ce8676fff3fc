import React, { useState } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  List,
  ListItem,
  ListItemText,
  Typography,
  ListItemButton
} from '@mui/material';
import { useTranslation } from 'react-i18next';
import { FolderItem } from '../types';

interface MoveDialogProps {
  open: boolean;
  onClose: () => void;
  onMove: (destinationFolderId: string) => void;
  folders: FolderItem[];
  excludeId?: string; // Để loại trừ folder hiện tại và con của nó
}

const MoveDialog: React.FC<MoveDialogProps> = ({ open, onClose, onMove, folders, excludeId }) => {
  const { t } = useTranslation();
  const [selectedFolderId, setSelectedFolderId] = useState<string | null>(null);

  // Loại trừ folder hiện tại và con của nó nếu cần
  const filteredFolders = excludeId
    ? folders.filter(f => f.id !== excludeId && f.parentId !== excludeId)
    : folders;

  return (
    <Dialog open={open} onClose={onClose} maxWidth="xs" fullWidth>
      <DialogTitle>{t('moveDialog.title', 'Move to Folder')}</DialogTitle>
      <DialogContent>
        <Typography variant="body2" sx={{ mb: 2 }}>
          {t('moveDialog.selectDestination', 'Select a destination folder:')}
        </Typography>
        <List>
          {filteredFolders.map(folder => (
            <ListItemButton
              key={folder.id}
              selected={selectedFolderId === folder.id}
              onClick={() => setSelectedFolderId(folder.id)}
            >
              <ListItemText primary={folder.name} />
            </ListItemButton>
          ))}
        </List>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>{t('common.cancel')}</Button>
        <Button
          onClick={() => selectedFolderId && onMove(selectedFolderId)}
          disabled={!selectedFolderId}
          variant="contained"
        >
          {t('moveDialog.move', 'Move')}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default MoveDialog;